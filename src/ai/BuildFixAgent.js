const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('./ai-service');

/**
 * BuildFixAgent - AI驱动的构建错误修复代理
 * 
 * 专门负责：
 * - AI调用和错误分析
 * - 工具执行（文件读写、目录列表）
 * - 错误分类和修复策略
 * - 与BuildFixer协作完成修复任务
 */
class BuildFixAgent extends AIService {
  constructor(projectPath, options = {}) {
    const aiOptions = {
      ...options,
      logDir: options.logDir || path.join(projectPath, 'ai-logs')
    };
    super(aiOptions);

    this.projectPath = projectPath;
    this.options = {
      maxAttempts: 6,
      dryRun: false,
      verbose: false,
      ...options
    };

    // 工具定义 - 类似 Augment 的工具系统
    this.tools = [
      {
        name: 'read_file',
        description: '读取项目中的文件内容',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            }
          },
          required: ['file_path']
        }
      },
      {
        name: 'write_file',
        description: '写入或修改项目中的文件',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            },
            content: {
              type: 'string',
              description: '要写入的文件内容'
            }
          },
          required: ['file_path', 'content']
        }
      },
      {
        name: 'list_files',
        description: '列出项目目录中的文件',
        parameters: {
          type: 'object',
          properties: {
            directory: {
              type: 'string',
              description: '要列出的目录路径，相对于项目根目录'
            },
            pattern: {
              type: 'string',
              description: '文件匹配模式，如 \'*.vue\' 或 \'*.js\''
            }
          },
          required: ['directory']
        }
      }
    ];

    // 重复检测
    this.attemptHistory = {
      filesToFix: [], // 记录每次尝试修复的文件列表
      errorHashes: [], // 记录每次的错误哈希
      lastErrorOutput: null // 记录上次的错误输出
    };

    // 修复统计
    this.fixStats = {
      filesAnalyzed: 0,
      filesModified: 0,
      errorsFixed: 0,
      attempts: 0
    };
  }

  /**
   * 分析构建错误并选择需要修复的文件
   */
  async analyzeBuildErrors(buildOutput, attemptNumber = 1) {
    if (!this.isEnabled()) {
      throw new Error('AI 服务不可用，无法进行错误分析');
    }

    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`🔍 构建错误输出长度: ${buildOutput.length} 字符`));
      }

      const prompt = this.generateAnalysisPrompt(buildOutput);
      
      // 添加轮次信息到日志上下文
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'error-analysis',
          attemptNumber: attemptNumber,
          phase: 'analysis',
          buildOutputLength: buildOutput.length
        }
      });

      if (this.options.verbose) {
        console.log(chalk.gray(`🤖 AI 响应长度: ${response.length} 字符`));
      }

      // 解析 AI 响应，提取要修复的文件列表
      const filesToFix = this.parseAnalysisResponse(response);

      if (filesToFix.length === 0) {
        throw new Error('AI 未能识别需要修复的文件');
      }

      console.log(chalk.gray(`✅ AI 识别出 ${filesToFix.length} 个需要修复的文件`));
      filesToFix.forEach(file => {
        console.log(chalk.gray(`  - ${file}`));
      });

      return {
        success: true,
        filesToFix
      };
    } catch (error) {
      console.log(chalk.red(`❌ AI 分析异常: ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 修复指定的文件列表
   */
  async fixFiles(filesToFix, buildOutput, attemptNumber = 1) {
    let filesModified = 0;
    const errors = [];

    for (let fileIndex = 0; fileIndex < filesToFix.length; fileIndex++) {
      const filePath = filesToFix[fileIndex];
      try {
        console.log(chalk.gray(`🔧 修复文件: ${filePath}`));

        // 读取文件内容
        const fileContent = await this.executeToolCall('read_file', { file_path: filePath });

        if (!fileContent.success) {
          console.log(chalk.yellow(`  ⚠️  无法读取文件: ${fileContent.error}`));
          errors.push(`无法读取文件 ${filePath}: ${fileContent.error}`);
          continue;
        }

        // 让 AI 修复文件，添加详细的上下文信息
        const fixResult = await this.fixSingleFile(
          filePath, 
          fileContent.content, 
          buildOutput, 
          attemptNumber,
          fileIndex + 1,
          filesToFix.length
        );

        if (fixResult.success) {
          // 写入修复后的文件
          const writeResult = await this.executeToolCall('write_file', {
            file_path: filePath,
            content: fixResult.fixedContent
          });

          if (writeResult.success) {
            console.log(chalk.green('  ✅ 文件修复成功'));
            filesModified++;
            this.fixStats.filesModified++;
          } else {
            console.log(chalk.yellow(`  ⚠️  无法写入文件: ${writeResult.error}`));
            errors.push(`无法写入文件 ${filePath}: ${writeResult.error}`);
          }
        } else {
          console.log(chalk.yellow(`  ⚠️  AI 修复失败: ${fixResult.error}`));
          errors.push(`AI 修复失败 ${filePath}: ${fixResult.error}`);
        }
      } catch (error) {
        console.log(chalk.red(`  ❌ 修复异常: ${error.message}`));
        errors.push(`修复异常 ${filePath}: ${error.message}`);
      }
    }

    this.fixStats.filesAnalyzed += filesToFix.length;
    this.fixStats.attempts++;

    return {
      filesModified,
      errors,
      totalFiles: filesToFix.length
    };
  }

  /**
   * 执行工具调用
   */
  async executeToolCall(toolName, parameters) {
    try {
      switch (toolName) {
        case 'read_file':
          return await this.readFile(parameters.file_path);
        case 'write_file':
          return await this.writeFile(parameters.file_path, parameters.content);
        case 'list_files':
          return await this.listFiles(parameters.directory, parameters.pattern);
        default:
          return {
            success: false,
            error: `未知的工具: ${toolName}`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 读取文件工具
   */
  async readFile(filePath) {
    try {
      const fullPath = path.join(this.projectPath, filePath);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '文件不存在'
        };
      }

      const content = await fs.readFile(fullPath, 'utf8');
      return {
        success: true,
        content
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 写入文件工具
   */
  async writeFile(filePath, content) {
    try {
      if (this.options.dryRun) {
        console.log(chalk.gray(`  [预览模式] 将写入文件: ${filePath}`));
        return {
          success: true,
          message: '预览模式，未实际写入'
        };
      }

      const fullPath = path.join(this.projectPath, filePath);

      // 确保目录存在
      await fs.ensureDir(path.dirname(fullPath));

      // 备份原文件
      if (await fs.pathExists(fullPath)) {
        const backupPath = `${fullPath}.backup.${Date.now()}`;
        await fs.copy(fullPath, backupPath);
      }

      await fs.writeFile(fullPath, content, 'utf8');
      return {
        success: true,
        message: '文件写入成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 列出文件工具
   */
  async listFiles(directory, pattern) {
    try {
      const fullPath = path.join(this.projectPath, directory);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '目录不存在'
        };
      }

      const files = await fs.readdir(fullPath);
      let filteredFiles = files;

      if (pattern) {
        const glob = require('glob');
        filteredFiles = files.filter(file => glob.minimatch(file, pattern));
      }

      return {
        success: true,
        files: filteredFiles
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查是否重复尝试相同的修复
   */
  isRepeatingAttempt(buildOutput) {
    // 生成当前错误的哈希
    const currentErrorHash = this.generateErrorHash(buildOutput);

    // 检查是否已经尝试过相同的错误
    if (this.attemptHistory.errorHashes.includes(currentErrorHash)) {
      return true;
    }

    // 检查错误输出是否与上次完全相同
    if (this.attemptHistory.lastErrorOutput === buildOutput) {
      return true;
    }

    return false;
  }

  /**
   * 生成错误哈希
   */
  generateErrorHash(buildOutput) {
    // 提取关键错误信息
    const errorLines = buildOutput.split('\n').filter(line => {
      const lowerLine = line.toLowerCase();
      return lowerLine.includes('error') ||
             lowerLine.includes('failed') ||
             lowerLine.includes('validationerror');
    });

    // 生成简单哈希
    return errorLines.join('|').replace(/\s+/g, ' ').trim();
  }

  /**
   * 记录本次尝试
   */
  recordAttempt(buildOutput, filesToFix) {
    const errorHash = this.generateErrorHash(buildOutput);

    this.attemptHistory.errorHashes.push(errorHash);
    this.attemptHistory.filesToFix.push([...filesToFix]);
    this.attemptHistory.lastErrorOutput = buildOutput;

    // 只保留最近3次尝试的记录
    if (this.attemptHistory.errorHashes.length > 3) {
      this.attemptHistory.errorHashes.shift();
      this.attemptHistory.filesToFix.shift();
    }
  }

  /**
   * 修复单个文件
   */
  async fixSingleFile(filePath, fileContent, buildOutput, attemptNumber = 1, fileIndex = 1, totalFiles = 1) {
    try {
      const prompt = this.generateFixPrompt(filePath, fileContent, buildOutput);
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'file-fix',
          attemptNumber: attemptNumber,
          phase: 'fix',
          fileName: path.basename(filePath),
          fileType: path.extname(filePath),
          fileIndex: fileIndex,
          totalFiles: totalFiles,
          buildOutputLength: buildOutput.length
        }
      });

      // 解析修复后的文件内容
      const fixedContent = this.parseFixResponse(response, fileContent);

      if (!fixedContent || fixedContent === fileContent) {
        return {
          success: false,
          error: 'AI 未能生成有效的修复内容'
        };
      }

      return {
        success: true,
        fixedContent
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成错误分析提示词
   */
  generateAnalysisPrompt(buildOutput) {
    // 截取构建输出，避免过长
    const maxOutputLength = 8000;
    const truncatedOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家和构建错误分析师。请分析以下构建错误输出，并确定需要修复的文件。

**任务目标**：
1. 分析构建错误输出
2. 识别导致错误的具体文件
3. 返回需要修复的文件路径列表

**构建错误输出**：
\`\`\`
${truncatedOutput}
\`\`\`

**工具可用**：
你可以使用以下工具来帮助分析：
${this.tools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

**响应格式**：
请使用以下 XML 格式返回分析结果：

\`\`\`xml
<analysis>
<files_to_fix>
<file>src/components/Example.vue</file>
<file>src/utils/helper.js</file>
</files_to_fix>
<reasoning>
简要说明为什么选择这些文件进行修复
</reasoning>
</analysis>
\`\`\`

请仔细分析错误信息，重点关注：
- 文件路径和行号信息
- 模块导入错误
- Vue 2/3 兼容性问题
- TypeScript 类型错误
- 依赖包问题
- Webpack 配置错误

**重要约束**：
- 只返回项目源代码文件，不要包含 node_modules 中的文件
- 对于 Webpack 配置错误，应该检查 vue.config.js、webpack.config.js 等配置文件
- 对于插件错误，重点关注项目配置而非第三方库内部文件
- 文件路径应该相对于项目根目录

只返回确实需要修改代码的文件，不要包含 node_modules 或系统文件。`;
  }

  /**
   * 解析 AI 分析响应
   */
  parseAnalysisResponse(response) {
    try {
      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<analysis>[\s\S]*?<files_to_fix>([\s\S]*?)<\/files_to_fix>[\s\S]*?<\/analysis>/);

      if (xmlMatch) {
        const filesSection = xmlMatch[1];
        const fileMatches = filesSection.match(/<file>(.*?)<\/file>/g);

        if (fileMatches) {
          return fileMatches.map(match => {
            let file = match.replace(/<\/?file>/g, '').trim();
            // 如果是绝对路径，转换为相对路径
            if (file.startsWith(this.projectPath)) {
              file = path.relative(this.projectPath, file);
            }
            return file;
          }).filter(file => this.isValidProjectFile(file));
        }
      }

      // 回退：尝试从响应中提取文件路径
      const lines = response.split('\n');
      const files = [];

      for (const line of lines) {
        // 查找看起来像文件路径的行
        const fileMatch = line.match(/(?:src\/|\.\/)?[\w/\-.]+\.(vue|js|ts|jsx|tsx)$/);
        if (fileMatch) {
          files.push(fileMatch[0]);
        }
      }

      return [...new Set(files)].filter(file => this.isValidProjectFile(file)); // 去重并过滤
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 分析响应失败，使用空列表'));
      return [];
    }
  }

  /**
   * 验证文件是否为有效的项目文件
   */
  isValidProjectFile(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      return false;
    }

    // 过滤掉 node_modules 中的文件
    if (filePath.includes('node_modules')) {
      console.log(chalk.yellow(`  ⚠️  跳过 node_modules 文件: ${filePath}`));
      return false;
    }

    // 过滤掉系统路径
    if (path.isAbsolute(filePath) && !filePath.startsWith(this.projectPath)) {
      console.log(chalk.yellow(`  ⚠️  跳过系统文件: ${filePath}`));
      return false;
    }

    // 只允许特定类型的文件
    const allowedExtensions = ['.vue', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss'];
    const allowedConfigFiles = ['vue.config.js', 'webpack.config.js', 'vite.config.js', 'vite.config.ts'];

    const ext = path.extname(filePath);
    const fileName = path.basename(filePath);

    if (allowedExtensions.includes(ext) || allowedConfigFiles.includes(fileName)) {
      return true;
    }

    console.log(chalk.yellow(`  ⚠️  跳过不支持的文件类型: ${filePath}`));
    return false;
  }

  /**
   * 生成文件修复提示词
   */
  generateFixPrompt(filePath, fileContent, buildOutput) {
    const fileExtension = path.extname(filePath);

    // 分析错误类型，生成针对性的提示词
    const errorAnalysis = this.analyzeErrorType(buildOutput);

    if (errorAnalysis.isWebpackConfigError) {
      return this.generateWebpackConfigFixPrompt(filePath, fileContent, buildOutput, errorAnalysis);
    }

    return this.generateGeneralFixPrompt(filePath, fileContent, buildOutput, fileExtension);
  }

  /**
   * 分析错误类型
   */
  analyzeErrorType(buildOutput) {
    const analysis = {
      isWebpackConfigError: false,
      isPluginError: false,
      isLoaderError: false,
      isSyntaxError: false,
      isImportError: false,
      specificErrors: []
    };

    const lowerBuildOutput = buildOutput.toLowerCase();

    // 检查是否是 webpack 配置错误
    if (lowerBuildOutput.includes('validationerror: invalid configuration object') ||
        lowerBuildOutput.includes('configuration.plugins') ||
        lowerBuildOutput.includes('misses the property \'apply\'') ||
        lowerBuildOutput.includes('configuration.plugins\\[(\\d+)\\] misses the property \'apply\'') ||
        lowerBuildOutput.includes('cannot call .tap() on a plugin that has not yet been defined')) {
      analysis.isWebpackConfigError = true;
      analysis.isPluginError = true;
    }

    // 检查 webpack is not defined
    if (lowerBuildOutput.includes('referenceerror: webpack is not defined')) {
      analysis.isWebpackConfigError = true;
      analysis.specificErrors.push({
        type: 'webpack_not_defined',
        message: 'ReferenceError: webpack is not defined'
      });
    }

    // 检查 path is not defined
    if (lowerBuildOutput.includes('referenceerror: path is not defined')) {
      analysis.isWebpackConfigError = true;
      analysis.specificErrors.push({
        type: 'path_not_defined',
        message: 'ReferenceError: path is not defined'
      });
    }

    // 检查 Webpack 5 polyfill 问题
    const polyfillMatches = buildOutput.match(/Module not found: Error: Can't resolve '([^']+)'/g);
    if (polyfillMatches) {
      const missingModules = [];
      polyfillMatches.forEach(match => {
        const moduleMatch = match.match(/Can't resolve '([^']+)'/);
        if (moduleMatch) {
          const moduleName = moduleMatch[1];
          // 检查是否是 Node.js 核心模块
          const nodeModules = ['path', 'stream', 'crypto', 'buffer', 'util', 'fs', 'os', 'http', 'https', 'url', 'querystring'];
          if (nodeModules.includes(moduleName)) {
            missingModules.push(moduleName);
          }
        }
      });
      
      if (missingModules.length > 0) {
        analysis.isWebpackConfigError = true;
        analysis.specificErrors.push({
          type: 'webpack5_polyfill_missing',
          modules: [...new Set(missingModules)], // 去重
          message: `Webpack 5 polyfill missing for: ${missingModules.join(', ')}`
        });
      }
    }

    // 检查无效选项
    const invalidOptionMatch = buildOutput.match(/Invalid options in .*? "(.+?)" is not allowed/);
    if (invalidOptionMatch) {
      analysis.isWebpackConfigError = true;
      analysis.specificErrors.push({
        type: 'invalid_option',
        option: invalidOptionMatch[1],
        message: invalidOptionMatch[0]
      });
    }
    
    // 检查 webpack-chain tap 错误
    if (lowerBuildOutput.includes('cannot call .tap() on a plugin that has not yet been defined')) {
      analysis.isWebpackConfigError = true;
      analysis.specificErrors.push({
        type: 'webpack_chain_tap_error',
        message: 'Cannot call .tap() on a plugin that has not yet been defined'
      });
    }

    // 提取具体的插件错误信息
    const pluginErrorMatch = buildOutput.match(/configuration\\.plugins\\[(\\d+)\\] misses the property 'apply'/);
    if (pluginErrorMatch) {
      analysis.specificErrors.push({
        type: 'plugin_missing_apply',
        pluginIndex: parseInt(pluginErrorMatch[1]),
        message: pluginErrorMatch[0]
      });
    }

    // 检查其他错误类型
    if (buildOutput.includes('Module not found') || buildOutput.includes('Cannot resolve module')) {
      analysis.isImportError = true;
    }

    if (buildOutput.includes('SyntaxError') || buildOutput.includes('Unexpected token')) {
      analysis.isSyntaxError = true;
    }

    return analysis;
  }

  /**
   * 生成 webpack 配置修复提示词
   */
  generateWebpackConfigFixPrompt(filePath, fileContent, buildOutput, errorAnalysis) {
    let specificGuidance = `
**首要修复任务**：请对整个 'vue.config.js' 文件进行全面审查和修复，使其与 Vue CLI 5 和 Vue 3 完全兼容。不要只修复一个错误，请一次性解决所有潜在问题。`;

    if (errorAnalysis.specificErrors.length > 0) {
      specificGuidance += '\n\n**具体错误分析**：\n';

      for (const error of errorAnalysis.specificErrors) {
        switch (error.type) {
          case 'webpack_not_defined':
            specificGuidance += `- **错误**: 'webpack is not defined'.\n  - **修复**: 在文件顶部添加 \`const webpack = require('webpack');\`.\n`;
            break;
          case 'path_not_defined':
            specificGuidance += `- **错误**: 'path is not defined'.\n  - **修复**: 在文件顶部添加 \`const path = require('path');\`.\n`;
            break;
          case 'webpack5_polyfill_missing':
            const modules = error.modules || [];
            const polyfillPackages = modules.map(mod => `${mod}-browserify`).join(' ');
            specificGuidance += `- **错误**: Webpack 5 缺少 Node.js 核心模块 polyfill: ${modules.join(', ')}.\n`;
            specificGuidance += `  - **修复步骤**:\n`;
            specificGuidance += `    1. 安装 polyfill 包: \`npm install -D ${polyfillPackages}\`\n`;
            specificGuidance += `    2. 在 configureWebpack.resolve.fallback 中配置:\n`;
            modules.forEach(mod => {
              specificGuidance += `       "${mod}": require.resolve("${mod}-browserify"),\n`;
            });
            break;
          case 'invalid_option':
            specificGuidance += `- **错误**: 无效的配置项 '${error.option}'.\n  - **修复**: 从配置中移除 \`${error.option}\` 属性。\n`;
            break;
          case 'plugin_missing_apply':
            specificGuidance += `- **错误**: 插件缺少 'apply' 方法 (plugins[${error.pluginIndex}]).\n  - **修复**: 检查该插件的配置，确保传入的是一个有效的插件实例，而不是空对象。\n`;
            break;
          case 'webpack_chain_tap_error':
            specificGuidance += `- **错误**: 在未定义的插件上调用 .tap()。\n  - **修复**: Vue CLI v5 已内置 preload 插件，通常不再需要手动配置。请移除关于 'preload' 和 'prefetch' 的 chainWebpack 修改。如果确实需要，请确保先使用 \`.use()\` 添加插件。\n`;
            break;
        }
      }
    }

    return `你是一个顶级的 Vue 迁移专家，精通 webpack 和 Vue CLI 配置。请修复以下 webpack 配置文件中的所有错误。

**任务目标**：全面修复 'vue.config.js'，使其兼容 Vue CLI 5 和 Vue 3，确保项目能成功构建。

**文件路径**：${filePath}

**构建错误日志**：
\`\`\`
${buildOutput}
\`\`\`
${specificGuidance}

**当前文件内容**：
\`\`\`js
${fileContent}
\`\`\`

**webpack 配置修复清单 (必须全部检查)**：
1.  **导入模块**: 确保文件顶部分别有 \`const webpack = require('webpack');\` 和 \`const path = require('path');\`（如果用到了的话）。
2.  **Webpack 5 Polyfill**: 如果遇到 "Module not found: Error: Can't resolve 'path'" 等错误，需要配置 Node.js 核心模块的 polyfill。
3.  **移除无效选项**: 移除根级别不支持的配置项，例如 \`name\`。Vue CLI 5 的配置项更加严格。
4.  **修复 chainWebpack**: 
    - Vue CLI 5 默认处理了 preload/prefetch 插件。请删除 \`config.plugin('preload').tap(...)\` 和 \`config.plugins.delete('prefetch')\` 的相关代码。
    - 检查其他插件的使用，确保语法正确。
5.  **devServer**: 确保 \`devServer.before\` 的用法是正确的。在 webpack-dev-server v4 (Vue CLI 5 使用) 中，它已被替换为 \`devServer.setupMiddlewares\`。请进行相应迁移。
6.  **输出为 Vue 3 格式**: 确保整个文件都是有效的 Vue 3 和 Vue CLI 5 配置。

**Webpack 5 Polyfill 配置示例**：
如果需要 Node.js 核心模块 polyfill，请在 configureWebpack 中添加：
\`\`\`js
configureWebpack: {
  resolve: {
    fallback: {
      "path": require.resolve("path-browserify"),
      "stream": require.resolve("stream-browserify"),
      // 根据需要添加其他模块
    }
  }
}
\`\`\`

**响应格式**：
请在下方 XML 格式的 <content> 标签内，返回修复后的【完整文件内容】。

\`\`\`xml
<file_fix>
<path>${filePath}</path>
<content>
// 在这里放入修复后的完整文件内容
</content>
</file_fix>
\`\`\`

请仔细分析并一次性修复所有问题。`;
  }

  /**
   * 生成通用修复提示词
   */
  generateGeneralFixPrompt(filePath, fileContent, buildOutput, fileExtension) {
    // 检查是否是模块导入相关的错误
    let importGuidance = '';
    if (buildOutput.includes('is not defined') || buildOutput.includes('Cannot access')) {
      importGuidance = `
**模块导入问题检测**：
检测到可能的模块导入错误，请特别注意：
- 检查所有外部模块是否都有正确的导入语句
- 确保 require/import 语句在文件顶部
- 检查模块名称拼写是否正确
- 确保依赖包已正确安装

**常见导入修复**：
- 添加缺失的导入：const webpack = require('webpack')
- 修复 path 导入：const path = require('path')
- 检查所有 require 语句的位置和正确性`;
    }

    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。请修复以下文件中的构建错误。

**任务目标**：修复 Vue 2 到 Vue 3 迁移过程中的构建错误，确保项目能够成功构建。

**文件信息**：
- 文件路径：${filePath}
- 文件类型：${fileExtension}

**构建错误输出**：
\`\`\`
${buildOutput}
\`\`\`
${importGuidance}

**当前文件内容**：
\`\`\`${fileExtension.slice(1) || 'text'}
${fileContent}
\`\`\`

**修复要求**：
1. 保持原有功能不变
2. 使用 Vue 3 兼容的语法
3. 更新导入语句和组件使用方式
4. 修复 TypeScript 类型错误
5. 确保代码风格一致
6. **重要**：检查并修复所有模块导入问题

**响应格式**：
请使用以下 XML 格式返回修复后的完整文件内容：

\`\`\`xml
<file_fix>
<path>${filePath}</path>
<content>
修复后的完整文件内容
</content>
</file_fix>
\`\`\`

**常见修复模式**：
- Vue 2 → Vue 3: new Vue() → createApp()
- Element UI → Element Plus: 更新导入路径和组件名称
- Vue Router: 更新路由配置语法
- Vuex: 更新状态管理语法
- 组合式 API: 使用 ref, reactive, computed 等
- **模块导入**: 添加缺失的 require/import 语句

**特别注意**：
- 如果错误包含 "is not defined"，检查是否缺少相应的导入语句
- 确保所有外部依赖都有正确的导入
- 检查导入语句的位置和顺序

请仔细分析错误信息，提供准确的修复方案。`;
  }

  /**
   * 解析修复响应
   */
  parseFixResponse(response, originalContent) {
    try {
      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<file_fix>[\s\S]*?<content>([\s\S]*?)<\/content>[\s\S]*?<\/file_fix>/);

      if (xmlMatch) {
        const content = xmlMatch[1].trim();
        if (content) {
          // 改进的内容验证逻辑
          return this.validateFixedContent(content, originalContent);
        }
      }

      // 回退：尝试解析代码块格式
      const codeBlockMatch = response.match(/```(?:vue|js|ts|javascript|typescript)?\s*([\s\S]*?)\s*```/);

      if (codeBlockMatch) {
        const content = codeBlockMatch[1].trim();
        if (content) {
          return this.validateFixedContent(content, originalContent);
        }
      }

      return null;
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 修复响应失败'));
      return null;
    }
  }

  /**
   * 验证修复后的内容
   */
  validateFixedContent(fixedContent, originalContent) {
    if (!fixedContent) {
      return null;
    }

    // 标准化内容进行比较（移除多余空格和换行）
    const normalizeContent = (content) => {
      return content
        .replace(/\r\n/g, '\n') // 统一换行符
        .replace(/\s+$/gm, '') // 移除行尾空格
        .trim(); // 移除首尾空格
    };

    const normalizedFixed = normalizeContent(fixedContent);
    const normalizedOriginal = normalizeContent(originalContent);

    // 如果内容完全相同，检查是否是有意的"无需修复"响应
    if (normalizedFixed === normalizedOriginal) {
      // 检查响应中是否包含"无需修复"的指示
      const noFixIndicators = [
        '无需修复',
        'no fix needed',
        'already correct',
        '已经正确',
        'file is correct'
      ];

      // 如果 AI 明确表示无需修复，则返回原内容（表示处理成功）
      // 否则返回 null（表示修复失败）
      const hasNoFixIndicator = noFixIndicators.some(indicator =>
        fixedContent.toLowerCase().includes(indicator.toLowerCase())
      );

      if (hasNoFixIndicator) {
        console.log(chalk.gray('  ℹ️  AI 判断文件无需修复'));
        return originalContent;
      } else {
        console.log(chalk.yellow('  ⚠️  AI 返回的内容与原文件相同，可能修复失败'));
        return null;
      }
    }

    // 基本的内容有效性检查
    if (normalizedFixed.length < 10) {
      console.log(chalk.yellow('  ⚠️  修复后的内容过短，可能无效'));
      return null;
    }

    // 检查是否包含基本的文件结构（针对不同文件类型）
    const hasValidStructure = this.validateFileStructure(fixedContent, originalContent);
    if (!hasValidStructure) {
      console.log(chalk.yellow('  ⚠️  修复后的内容缺少必要的文件结构'));
      return null;
    }

    return fixedContent;
  }

  /**
   * 验证文件结构的有效性
   */
  validateFileStructure(fixedContent, originalContent) {
    // 对于 Vue 文件，检查基本结构
    if (originalContent.includes('<template>') || originalContent.includes('<script>')) {
      // Vue 文件应该保持基本的 Vue 组件结构
      const hasTemplate = fixedContent.includes('<template>') || !originalContent.includes('<template>');
      const hasScript = fixedContent.includes('<script>') || !originalContent.includes('<script>');
      return hasTemplate && hasScript;
    }

    // 对于 JS/TS 文件，检查基本的导入导出结构
    if (originalContent.includes('export') || originalContent.includes('module.exports')) {
      // 应该保持导出语句
      return fixedContent.includes('export') || fixedContent.includes('module.exports');
    }

    // 对于 SCSS/CSS 文件，检查基本的样式结构
    if (originalContent.includes('{') && originalContent.includes('}')) {
      // 应该保持基本的 CSS 语法结构
      const openBraces = (fixedContent.match(/\{/g) || []).length;
      const closeBraces = (fixedContent.match(/\}/g) || []).length;
      return openBraces === closeBraces && openBraces > 0;
    }

    // 对于其他文件类型，默认认为有效
    return true;
  }

  /**
   * 生成修复会话摘要
   */
  async generateSessionSummary() {
    try {
      const sessionId = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const summaryPath = path.join(this.options.logDir, `session-summary-${sessionId}.json`);
      
      // 读取所有相关的日志文件
      const logFiles = await this.getSessionLogFiles();
      const sessionData = {
        sessionId: sessionId,
        projectPath: this.projectPath,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        totalAttempts: this.fixStats.attempts,
        filesAnalyzed: this.fixStats.filesAnalyzed,
        filesModified: this.fixStats.filesModified,
        attempts: []
      };

      // 按轮次组织日志数据
      for (const logFile of logFiles) {
        try {
          const logData = await fs.readJson(logFile);
          const attemptNumber = logData.context?.attemptNumber || 1;
          
          if (!sessionData.attempts[attemptNumber - 1]) {
            sessionData.attempts[attemptNumber - 1] = {
              attemptNumber: attemptNumber,
              phases: []
            };
          }
          
          sessionData.attempts[attemptNumber - 1].phases.push({
            phase: logData.context?.phase || 'unknown',
            taskType: logData.context?.taskType || 'unknown',
            fileName: logData.context?.fileName || 'unknown',
            success: logData.success,
            duration_ms: logData.duration_ms,
            timestamp: logData.timestamp,
            logFile: path.basename(logFile)
          });
        } catch (error) {
          console.warn(chalk.yellow(`⚠️  无法读取日志文件: ${logFile}`));
        }
      }

      // 写入会话摘要
      await fs.writeJson(summaryPath, sessionData, { spaces: 2 });
      console.log(chalk.blue(`📊 会话摘要已生成: ${path.basename(summaryPath)}`));
      
      return summaryPath;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  生成会话摘要失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 获取会话相关的日志文件
   */
  async getSessionLogFiles() {
    try {
      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .map(file => path.join(this.options.logDir, file))
        .sort();

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法读取日志目录: ${error.message}`));
      return [];
    }
  }

  /**
   * 获取修复统计信息
   */
  getFixStats() {
    return {
      ...this.fixStats,
      aiStats: this.getStats()
    };
  }

  /**
   * 重置修复统计
   */
  resetFixStats() {
    this.fixStats = {
      filesAnalyzed: 0,
      filesModified: 0,
      errorsFixed: 0,
      attempts: 0
    };
    this.resetStats();
  }

  /**
   * 列出所有轮次的日志文件
   */
  async listSessionLogs() {
    try {
      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .sort();
      
      if (logFiles.length === 0) {
        console.log(chalk.gray('📝 没有找到 AI 调用日志文件'));
        return [];
      }

      console.log(chalk.blue(`📝 找到 ${logFiles.length} 个 AI 调用日志文件:`));
      
      // 按轮次分组显示
      const attempts = {};
      for (const file of logFiles) {
        const match = file.match(/attempt(\d+)/);
        if (match) {
          const attemptNum = parseInt(match[1]);
          if (!attempts[attemptNum]) {
            attempts[attemptNum] = [];
          }
          attempts[attemptNum].push(file);
        }
      }

      // 显示每个轮次的日志
      Object.keys(attempts).sort((a, b) => parseInt(a) - parseInt(b)).forEach(attemptNum => {
        console.log(chalk.gray(`\n  轮次 ${attemptNum}:`));
        attempts[attemptNum].forEach(file => {
          console.log(chalk.gray(`    - ${file}`));
        });
      });

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法列出日志文件: ${error.message}`));
      return [];
    }
  }
}

module.exports = BuildFixAgent; 